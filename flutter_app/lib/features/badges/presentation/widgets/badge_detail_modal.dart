import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import '../../models/badge_model.dart';
import 'parallax_badge_icon.dart';
import 'animations/confetti_celebration.dart';
import 'animations/element_fly_in.dart';
import 'animations/lock_shake.dart';
import 'animations/water_wave_progress.dart';

/// 徽章详情模态框
class BadgeDetailModal extends StatefulWidget {
  final BadgeModel badge;

  const BadgeDetailModal({
    super.key,
    required this.badge,
  });

  @override
  State<BadgeDetailModal> createState() => _BadgeDetailModalState();
}

class _BadgeDetailModalState extends State<BadgeDetailModal>
    with TickerProviderStateMixin {
  bool _showCelebration = false;
  bool _showFlyInAnimation = false;
  bool _showLockShake = false;

  @override
  void initState() {
    super.initState();

    // 延迟启动动画，让页面先渲染
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        if (widget.badge.earned) {
          // 已获得徽章：启动礼炮和飞入动画
          setState(() {
            _showCelebration = true;
          });

          // 礼炮动画完成后启动元素飞入
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              setState(() {
                _showFlyInAnimation = true;
              });
            }
          });
        } else {
          // 未获得徽章：启动锁摇动动画
          setState(() {
            _showLockShake = true;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFFE5E7EB),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 内容区域
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // 徽章图标
                  _buildBadgeIcon(),
                  const SizedBox(height: 24),

                  // 徽章信息（带飞入动画）
                  ElementFlyIn(
                    isActive: _showFlyInAnimation,
                    delay: const Duration(milliseconds: 200),
                    child: _buildBadgeInfo(),
                  ),
                  const SizedBox(height: 32),

                  // 进度信息（带飞入动画）
                  if (!widget.badge.earned)
                    ElementFlyIn(
                      isActive: _showFlyInAnimation,
                      delay: const Duration(milliseconds: 400),
                      child: _buildProgressInfo(),
                    ),

                  // 获得信息（带礼炮动画）
                  if (widget.badge.earned)
                    ElementFlyIn(
                      isActive: _showFlyInAnimation,
                      delay: const Duration(milliseconds: 300),
                      child: _buildEarnedInfo(),
                    ),
                ],
              ),
            ),
          ),
          
          // 底部按钮
          _buildBottomButton(context),
        ],
      ),
    );
  }

  /// 构建徽章图标
  Widget _buildBadgeIcon() {
    // 如果是已获得的徽章，使用视差3D效果
    if (widget.badge.earned) {
      final isMobile = MediaQuery.of(context).size.width < 600;
      
      return isMobile
          ? ParallaxBadgeIcon(
              iconType: widget.badge.icon,
              colorFront: _getColor(),
              colorSide: _getColor().withOpacity(0.7),
              bgColor: Colors.white,
              size: 120,
              depth: 20.0,
              gloss: 0.35,
              enableSensor: true,
            )
          : ParallaxBadgeIconHover(
              iconType: widget.badge.icon,
              colorFront: _getColor(),
              colorSide: _getColor().withOpacity(0.7),
              bgColor: Colors.white,
              size: 120,
              depth: 20.0,
              gloss: 0.35,
            );
    }

    // 未获得的徽章使用原来的静态样式
    Widget badgeContainer = Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: _getBgColor(),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: const Color(0xFFE5E7EB),
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(0, 0, 0, 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        children: [
          Center(
            child: Icon(
              _getIconData(),
              size: 48,
              color: _getColor(),
            ),
          ),
          
          // 未获得遮罩（带锁摇动动画）
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(27),
            ),
            child: Center(
              child: EnhancedLockShake(
                isActive: _showLockShake,
                glowColor: Colors.white,
                child: const Icon(
                  Icons.lock_outline,
                  size: 32,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );

    return badgeContainer;
  }

  /// 构建徽章信息
  Widget _buildBadgeInfo() {
    return Column(
      children: [
        // 徽章名称
        Text(
          widget.badge.name,
          style: GoogleFonts.outfit(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF1F2937),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        // 徽章分类
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _getBgColor(),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            widget.badge.category,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: _getColor(),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // 徽章描述
        Text(
          widget.badge.description,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF6B7280),
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建进度信息 - 参考React UI设计
  Widget _buildProgressInfo() {
    return Container(
      padding: const EdgeInsets.all(16), // p-4
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFB), // bg-gray-50
        borderRadius: BorderRadius.circular(16), // rounded-2xl
      ),
      child: Column(
        children: [
          // 进度标题和图标
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.trending_up,
                color: Color(0xFF6366F1), // text-indigo-600
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'In Progress',
                style: GoogleFonts.outfit(
                  fontSize: 18,
                  fontWeight: FontWeight.w600, // font-semibold
                  color: const Color(0xFF374151), // text-gray-700
                ),
              ),
            ],
          ),
          const SizedBox(height: 12), // mb-3

          // 水波进度条 - 增强版动画效果
          Center(
            child: _buildWaterWaveProgress(),
          ),
        ],
      ),
    );
  }

  /// 构建水波进度条 - 增强版动画效果
  Widget _buildWaterWaveProgress() {
    final progressPercentage = widget.badge.target > 0 ? (widget.badge.progress / widget.badge.target).clamp(0.0, 1.0) : 0.0;
    const size = 80.0;

    return EnhancedWaterWaveProgress(
      progress: progressPercentage,
      size: size,
      waveColor: _getColor(),
      backgroundColor: const Color(0xFFE5E7EB),
      isAnimating: true,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(12),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.1),
              blurRadius: 4,
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${widget.badge.progress}',
              style: GoogleFonts.outfit(
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: const Color(0xFF1F2937),
              ),
            ),
            Text(
              '/${widget.badge.target}',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF6B7280),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建已获得信息
  Widget _buildEarnedInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _getBgColor(),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getColor().withOpacity(0.3)),
      ),
      child: Column(
        children: [
          // 恭喜图标（带礼炮动画）
          ConfettiCelebration(
            isActive: _showCelebration,
            child: Icon(
              Icons.celebration,
              size: 32,
              color: _getColor(),
            ),
          ),
          const SizedBox(height: 12),
          
          // 恭喜文本
          Text(
            'Congratulations!',
            style: GoogleFonts.outfit(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: _getColor(),
            ),
          ),
          const SizedBox(height: 8),
          
          // 获得日期
          if (widget.badge.earnedDate != null)
            Text(
              'Earned on ${_formatDate(widget.badge.earnedDate!)}',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF6B7280),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButton(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: SizedBox(
        width: double.infinity,
        height: 48,
        child: ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          style: ElevatedButton.styleFrom(
            backgroundColor: _getColor(),
            foregroundColor: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Text(
            'Close',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }



  /// 获取颜色
  Color _getColor() {
    return Color(int.parse(widget.badge.color.replaceFirst('#', '0xFF')));
  }

  /// 获取背景颜色
  Color _getBgColor() {
    return Color(int.parse(widget.badge.bgColor.replaceFirst('#', '0xFF')));
  }

  /// 获取图标数据
  IconData _getIconData() {
    switch (widget.badge.icon) {
      case 'fitness_center':
        return Icons.fitness_center;
      case 'emoji_events':
        return Icons.emoji_events;
      case 'local_fire_department':
        return Icons.local_fire_department;
      case 'whatshot':
        return Icons.whatshot;
      case 'wb_sunny':
        return Icons.wb_sunny;
      case 'military_tech':
        return Icons.military_tech;
      case 'directions_run':
        return Icons.directions_run;
      case 'self_improvement':
        return Icons.self_improvement;
      case 'favorite':
        return Icons.favorite;
      case 'schedule':
        return Icons.schedule;
      default:
        return Icons.emoji_events;
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}
